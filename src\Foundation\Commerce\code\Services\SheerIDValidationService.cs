namespace Pmi.Spx.Foundation.Commerce.Services
{
    using Pmi.Spx.Foundation.Commerce.JsModels.Cart;
    using Pmi.Spx.Foundation.Commerce.Models;
    using Pmi.Spx.Foundation.Framework.Services.Logging;
    using Pmi.Spx.Foundation.Framework.Services.Tasks;
    using Pmi.Spx.Foundation.Profile.Services;
    using Pmi.Spx.Foundation.Security.Models;
    using Sitecore.Diagnostics;
    using System;
    using System.Linq;

    public class SheerIDValidationService : ISheerIDValidationService
    {
        private readonly ISheerIDService sheerIDService;
        private readonly ILogger logger;
        private readonly ITaskService taskService;

        public SheerIDValidationService(ISheerIDService sheerIDService, ILoggerFactory loggerFactory, ITaskService taskService)
        {
            Assert.IsNotNull(sheerIDService, nameof(sheerIDService));
            Assert.IsNotNull(loggerFactory, nameof(loggerFactory));
            Assert.IsNotNull(taskService, nameof(taskService));

            this.sheerIDService = sheerIDService;
            this.logger = loggerFactory.GetLogger(this);
            this.taskService = taskService;
        }

        public SheerIDValidationResult ValidateSheerID(MembershipModel membership, string verificationId, User user)
        {
            if(string.IsNullOrWhiteSpace(verificationId))
            {
                return SheerIDValidationResult.Failed;
            }

            try
            {
                var verificationDetails = this.taskService.SyncInvoke(() => this.sheerIDService.GetSheerIDVerificationDetailsAsync(verificationId));
                var personInfo = verificationDetails?.PersonInfo;
                if(personInfo != null)
                {
                    var userFullName = new string(user?.FullName.Where(char.IsLetter).ToArray() ?? new char[0]).ToLower();
                    var personInfoLastName = new string(personInfo.LastName.Where(char.IsLetter).ToArray()).ToLower();
                    if(!userFullName.Contains(personInfoLastName))
                    {
                        return SheerIDValidationResult.Failed;
                    }
                }
                return SheerIDValidationResult.Passed;
            }
            catch(Exception ex)
            {
                this.logger.Error("SheerID service call error", ex);
                return SheerIDValidationResult.Failed;
            }
        }
    }
}