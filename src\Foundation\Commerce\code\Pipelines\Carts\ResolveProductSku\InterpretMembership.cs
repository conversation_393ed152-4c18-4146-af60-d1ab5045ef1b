namespace Pmi.Spx.Foundation.Commerce.Pipelines.Carts.ResolveProductSku
{
    using Pmi.Spx.Foundation.Commerce.Constants;
    using Pmi.Spx.Foundation.Commerce.Models;
    using Pmi.Spx.Foundation.Commerce.Repositories;
    using Pmi.Spx.Foundation.Commerce.ServiceProviderModels;
    using Pmi.Spx.Foundation.Commerce.Services;
    using Pmi.Spx.Foundation.Framework.Exceptions;
    using Pmi.Spx.Foundation.Profile.Constants;
    using Pmi.Spx.Foundation.Security.Services;
    using System;
    using System.Linq;

    public class InterpretMembership
    {
        private const string SHEERID_VERIFICATION_ERROR_CODE = "Connector.SheerIdValidationError";
        private const string MEMBERSHIP_NOT_FOUND_CODE = "Connector.MembershipNotFound";
        private const string STUDENT_MEMBERSHIP_PLAN_CODE = "PMISN";


        private readonly IUserService userService;
        private readonly IMembershipRepository membershipRepository;
        private readonly ISheerIDValidationService sheerIDValidationService;
        private readonly ICommerceConfiguration commerceConfiguration;


        public InterpretMembership(IUserService userService, IMembershipRepository membershipRepository, 
                                   ISheerIDValidationService sheerIDValidationService, ICommerceConfiguration commerceConfiguration)
        {
            this.userService = userService;
            this.membershipRepository = membershipRepository;
            this.sheerIDValidationService = sheerIDValidationService;
            this.commerceConfiguration = commerceConfiguration; 
        }

        public void Process(ResolveProductSkuArgs args)
        {
            if(!string.IsNullOrWhiteSpace(args.Result.ProductSku))
            {
                return;
            }

            var membershipCode = args.Request.CustomOptions
                                     .FirstOrDefault(x => x.Title.Equals(Membership.MembershipUrlParameter, 
                                      StringComparison.OrdinalIgnoreCase))?.Value;            

            if(string.IsNullOrWhiteSpace(membershipCode))
            {
                return;
            }

            var user = this.userService.GetCurrentUser();
            var loginInfo = this.userService.GetLoginInfo();

            var userRegisteredCountry = args.Request.Properties[CommercePipelineParameters.UserStoreResolvingCountryCodeKey] as string;

            var currentMembershipType = loginInfo?.MembershipInfo?.ExpirationDate < DateTime.UtcNow.Date ? null : loginInfo?.MembershipInfo?.MembershipType;
            var membershipItems = this.membershipRepository.GetMembershipItems();
            var membership = membershipItems.FirstOrDefault(x => x.Code.Equals(membershipCode,
                                                                 StringComparison.InvariantCultureIgnoreCase)
                                                             && (x.IsForMembersOnly == !string.IsNullOrWhiteSpace(currentMembershipType)));

            if(membership == null && membershipCode.ToLower().Contains("student"))
            {
                var isEligibleForStudentMembership = loginInfo?.MembershipInfo?.EligibleMemberships?
                                                    .Any(e => e.SourceKey.Equals(STUDENT_MEMBERSHIP_PLAN_CODE, 
                                                        StringComparison.CurrentCultureIgnoreCase))
                                                     ?? false;
                membership = membershipItems.FirstOrDefault(x => x.Code.Equals(membershipCode, 
                                                            StringComparison.InvariantCultureIgnoreCase) 
                                                            && isEligibleForStudentMembership);
            }

            if(membership == null)
            {
                throw new CodedException(MEMBERSHIP_NOT_FOUND_CODE, membershipCode);
            }

            if(membership.IsSheerIDVerificationRequired)
            {
                var verificationId = args.Request.CustomOptions
                                                 .FirstOrDefault(x => x.Title.Equals(SheerIDUrlParameters.VerificationId,StringComparison.OrdinalIgnoreCase))?.Value;
                var sheerIDValidationResult = this.sheerIDValidationService.ValidateSheerID(membership, verificationId, user);
                if(sheerIDValidationResult == SheerIDValidationResult.Failed)
                {
                    throw new CodedException(SHEERID_VERIFICATION_ERROR_CODE, verificationId);
                }
            }

            args.Result.CustomOpions = args.Request.CustomOptions.Where(x => !string.IsNullOrWhiteSpace(x.Value))
                .Select(x =>
                {
                    if(x.Title.Equals(CertAdminUrlParameters.ApplicationId, StringComparison.InvariantCultureIgnoreCase))
                    {
                        x.Title = CertAdminUrlParameters.Application;
                    }

                    return x;
                });

            args.Result.ProductSku = ShouldUseSingleMemberShipSku(userRegisteredCountry) &&
                                    !string.IsNullOrWhiteSpace(membership.SingleMembershipProductId) ? membership.SingleMembershipProductId
                                                                                                     : membership.ItemId;
        }



        private bool ShouldUseSingleMemberShipSku(string registrationCountry)
        {
            if(string.IsNullOrEmpty(registrationCountry))
                return false;

            var settings = this.commerceConfiguration.GetFeatureSwitch();
            var result = settings?.EnabledSingleMembershipCountries?.Any(x=>x.Code3.Equals(registrationCountry));
            return result ?? false;
        }
    }
}