
#pragma warning disable 1591
#pragma warning disable 0108
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by Team Development for Sitecore.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;   
using System.Collections.Generic;   
using System.Linq;
using System.Text;
using Glass.Mapper.Sc.Configuration.Attributes;
using Glass.Mapper.Sc.Configuration;
using Glass.Mapper.Sc.Fields;
using Sitecore.Globalization;
using Sitecore.Data;
using Pmi.Spx.Foundation.GlassMapper.Models;



namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IFeatureSwitch Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Teams Notification Settings/Feature Switch</para>	
	/// <para>ID: 0c49dde5-83d6-4654-a92b-a19c57da818d</para>	
	/// </summary>
	[SitecoreType(TemplateId=FeatureSwitch.TemplateId )] //, Cachable = true
	public partial interface IFeatureSwitch : IGlassBase 
	{		/// <summary>
		/// The Notify When Synchronize All Records Fails field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 515adbd7-bd3a-4d41-acf4-c0cab4b7152f</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSynchronizeAllRecordsFailsFieldName)]
		bool NotifyWhenSynchronizeAllRecordsFails  {get; set;} 
			/// <summary>
		/// The Notify When Synchronize All Records Succeeded field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 71b479f8-697e-462c-bdc6-fcf82a1dc52b</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSynchronizeAllRecordsSucceededFieldName)]
		bool NotifyWhenSynchronizeAllRecordsSucceeded  {get; set;} 
			/// <summary>
		/// The Notify When Automatic Product Synchronization Fails field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: f40385c5-d5c1-408c-a328-769cc19c72ce</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenAutomaticProductSynchronizationFailsFieldName)]
		bool NotifyWhenAutomaticProductSynchronizationFails  {get; set;} 
			/// <summary>
		/// The Notify When Automatic Product Synchronization Succeeded field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: eb4480e8-ebee-4396-8b6f-f741cc72731d</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenAutomaticProductSynchronizationSucceededFieldName)]
		bool NotifyWhenAutomaticProductSynchronizationSucceeded  {get; set;} 
			/// <summary>
		/// The Notify When Sync New and Multiple Products Fails field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: eb84f719-0eae-441d-9b95-ef0232b23515</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSyncNewAndMultipleProductsFailsFieldName)]
		bool NotifyWhenSyncNewAndMultipleProductsFails  {get; set;} 
			/// <summary>
		/// The Notify When Sync New and Multiple Products Succeeded field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: a15b9490-3fda-4330-8615-766d3d3ced10</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSyncNewAndMultipleProductsSucceededFieldName)]
		bool NotifyWhenSyncNewAndMultipleProductsSucceeded  {get; set;} 
			/// <summary>
		/// The Notify When Synchronize All Products Fails field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 6e770910-122d-4435-b90d-81293b8fa5bf</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSynchronizeAllProductsFailsFieldName)]
		bool NotifyWhenSynchronizeAllProductsFails  {get; set;} 
			/// <summary>
		/// The Notify When Synchronize All Products Succeeded field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 519d23c9-ce6e-4ddf-bf05-03aaacecb27e</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSynchronizeAllProductsSucceededFieldName)]
		bool NotifyWhenSynchronizeAllProductsSucceeded  {get; set;} 
			/// <summary>
		/// The Notify When Synchronize Current Store Fails field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 16b06776-4d4b-4a94-84d4-561e22215cf3</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSynchronizeCurrentStoreFailsFieldName)]
		bool NotifyWhenSynchronizeCurrentStoreFails  {get; set;} 
			/// <summary>
		/// The Notify When Synchronize Current Store Succeeded field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: fc873d8b-d2fc-495d-94f8-37073251971f</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSwitch.NotifyWhenSynchronizeCurrentStoreSucceededFieldName)]
		bool NotifyWhenSynchronizeCurrentStoreSucceeded  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class FeatureSwitch
	{
		public const string TemplateId = "0c49dde5-83d6-4654-a92b-a19c57da818d";
		public const string TemplateName = "Feature Switch";
				public const string NotifyWhenSynchronizeAllRecordsFailsFieldId = "515adbd7-bd3a-4d41-acf4-c0cab4b7152f";
		public const string NotifyWhenSynchronizeAllRecordsFailsFieldName = "Notify When Synchronize All Records Fails";			
				public const string NotifyWhenSynchronizeAllRecordsSucceededFieldId = "71b479f8-697e-462c-bdc6-fcf82a1dc52b";
		public const string NotifyWhenSynchronizeAllRecordsSucceededFieldName = "Notify When Synchronize All Records Succeeded";			
				public const string NotifyWhenAutomaticProductSynchronizationFailsFieldId = "f40385c5-d5c1-408c-a328-769cc19c72ce";
		public const string NotifyWhenAutomaticProductSynchronizationFailsFieldName = "Notify When Automatic Product Synchronization Fails";			
				public const string NotifyWhenAutomaticProductSynchronizationSucceededFieldId = "eb4480e8-ebee-4396-8b6f-f741cc72731d";
		public const string NotifyWhenAutomaticProductSynchronizationSucceededFieldName = "Notify When Automatic Product Synchronization Succeeded";			
				public const string NotifyWhenSyncNewAndMultipleProductsFailsFieldId = "eb84f719-0eae-441d-9b95-ef0232b23515";
		public const string NotifyWhenSyncNewAndMultipleProductsFailsFieldName = "Notify When Sync New and Multiple Products Fails";			
				public const string NotifyWhenSyncNewAndMultipleProductsSucceededFieldId = "a15b9490-3fda-4330-8615-766d3d3ced10";
		public const string NotifyWhenSyncNewAndMultipleProductsSucceededFieldName = "Notify When Sync New and Multiple Products Succeeded";			
				public const string NotifyWhenSynchronizeAllProductsFailsFieldId = "6e770910-122d-4435-b90d-81293b8fa5bf";
		public const string NotifyWhenSynchronizeAllProductsFailsFieldName = "Notify When Synchronize All Products Fails";			
				public const string NotifyWhenSynchronizeAllProductsSucceededFieldId = "519d23c9-ce6e-4ddf-bf05-03aaacecb27e";
		public const string NotifyWhenSynchronizeAllProductsSucceededFieldName = "Notify When Synchronize All Products Succeeded";			
				public const string NotifyWhenSynchronizeCurrentStoreFailsFieldId = "16b06776-4d4b-4a94-84d4-561e22215cf3";
		public const string NotifyWhenSynchronizeCurrentStoreFailsFieldName = "Notify When Synchronize Current Store Fails";			
				public const string NotifyWhenSynchronizeCurrentStoreSucceededFieldId = "fc873d8b-d2fc-495d-94f8-37073251971f";
		public const string NotifyWhenSynchronizeCurrentStoreSucceededFieldName = "Notify When Synchronize Current Store Succeeded";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IFeatureSwitch interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Feature Switch template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Teams Notification Settings/Feature Switch")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 0c49dde5-83d6-4654-a92b-a19c57da818d")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class FeatureSwitch_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public FeatureSwitch_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IFeatureSwitch interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Synchronize All Records Fails field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 515adbd7-bd3a-4d41-acf4-c0cab4b7152f")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSynchronizeAllRecordsFails  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Synchronize All Records Succeeded field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 71b479f8-697e-462c-bdc6-fcf82a1dc52b")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSynchronizeAllRecordsSucceeded  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Automatic Product Synchronization Fails field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: f40385c5-d5c1-408c-a328-769cc19c72ce")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenAutomaticProductSynchronizationFails  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Automatic Product Synchronization Succeeded field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: eb4480e8-ebee-4396-8b6f-f741cc72731d")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenAutomaticProductSynchronizationSucceeded  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Sync New and Multiple Products Fails field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: eb84f719-0eae-441d-9b95-ef0232b23515")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSyncNewAndMultipleProductsFails  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Sync New and Multiple Products Succeeded field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: a15b9490-3fda-4330-8615-766d3d3ced10")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSyncNewAndMultipleProductsSucceeded  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Synchronize All Products Fails field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 6e770910-122d-4435-b90d-81293b8fa5bf")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSynchronizeAllProductsFails  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Synchronize All Products Succeeded field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 519d23c9-ce6e-4ddf-bf05-03aaacecb27e")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSynchronizeAllProductsSucceeded  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Synchronize Current Store Fails field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 16b06776-4d4b-4a94-84d4-561e22215cf3")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSynchronizeCurrentStoreFails  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Notify When Synchronize Current Store Succeeded field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: fc873d8b-d2fc-495d-94f8-37073251971f")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox NotifyWhenSynchronizeCurrentStoreSucceeded  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// ITeamsNotificationSettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Teams Notification Settings/Teams Notification Settings</para>	
	/// <para>ID: 2aa0eba7-c529-4fc4-af49-c7c1cf9d8af2</para>	
	/// </summary>
	[SitecoreType(TemplateId=TeamsNotificationSettings.TemplateId )] //, Cachable = true
	public partial interface ITeamsNotificationSettings : IGlassBase 
	{	}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class TeamsNotificationSettings
	{
		public const string TemplateId = "2aa0eba7-c529-4fc4-af49-c7c1cf9d8af2";
		public const string TemplateName = "Teams Notification Settings";
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the ITeamsNotificationSettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Teams Notification Settings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Teams Notification Settings/Teams Notification Settings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 2aa0eba7-c529-4fc4-af49-c7c1cf9d8af2")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class TeamsNotificationSettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public TeamsNotificationSettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the ITeamsNotificationSettings interface instead");
		}
			}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// INewRelicLoggingSettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/NewRelicLoggingSettings</para>	
	/// <para>ID: 3400f942-cdda-46a0-bd78-be9c84129303</para>	
	/// </summary>
	[SitecoreType(TemplateId=NewRelicLoggingSettings.TemplateId )] //, Cachable = true
	public partial interface INewRelicLoggingSettings : IGlassBase 
	{		/// <summary>
		/// The EnableLogging field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 9b3bed2b-7132-4385-97ec-358c8550c393</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(NewRelicLoggingSettings.EnableLoggingFieldName)]
		bool EnableLogging  {get; set;} 
			/// <summary>
		/// The RequestUrlPatterns field.
		/// <para></para>
		/// <para>Field Type: Multi-Line Text</para>		
		/// <para>Field ID: b19fa00d-a62f-430d-a200-50a6282ce988</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(NewRelicLoggingSettings.RequestUrlPatternsFieldName)]
		string RequestUrlPatterns  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class NewRelicLoggingSettings
	{
		public const string TemplateId = "3400f942-cdda-46a0-bd78-be9c84129303";
		public const string TemplateName = "NewRelicLoggingSettings";
				public const string EnableLoggingFieldId = "9b3bed2b-7132-4385-97ec-358c8550c393";
		public const string EnableLoggingFieldName = "EnableLogging";			
				public const string RequestUrlPatternsFieldId = "b19fa00d-a62f-430d-a200-50a6282ce988";
		public const string RequestUrlPatternsFieldName = "RequestUrlPatterns";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the INewRelicLoggingSettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The NewRelicLoggingSettings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/NewRelicLoggingSettings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 3400f942-cdda-46a0-bd78-be9c84129303")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class NewRelicLoggingSettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public NewRelicLoggingSettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the INewRelicLoggingSettings interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The EnableLogging field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 9b3bed2b-7132-4385-97ec-358c8550c393")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox EnableLogging  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The RequestUrlPatterns field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Multi-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: b19fa00d-a62f-430d-a200-50a6282ce988")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreMultiLineText RequestUrlPatterns  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IFeatureSettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/FeatureSwitch/FeatureSettings</para>	
	/// <para>ID: 3df09670-e015-489a-bf5a-3589883d8ca9</para>	
	/// </summary>
	[SitecoreType(TemplateId=FeatureSettings.TemplateId )] //, Cachable = true
	public partial interface IFeatureSettings : IGlassBase 
	{		/// <summary>
		/// The IsCancelRefundQueueEnabled field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 15edcc2c-0906-45bc-8fcf-bb38a5b4158a</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(FeatureSettings.IsCancelRefundQueueEnabledFieldName)]
		bool IsCancelRefundQueueEnabled  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class FeatureSettings
	{
		public const string TemplateId = "3df09670-e015-489a-bf5a-3589883d8ca9";
		public const string TemplateName = "FeatureSettings";
				public const string IsCancelRefundQueueEnabledFieldId = "15edcc2c-0906-45bc-8fcf-bb38a5b4158a";
		public const string IsCancelRefundQueueEnabledFieldName = "IsCancelRefundQueueEnabled";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IFeatureSettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The FeatureSettings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/FeatureSwitch/FeatureSettings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 3df09670-e015-489a-bf5a-3589883d8ca9")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class FeatureSettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public FeatureSettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IFeatureSettings interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The IsCancelRefundQueueEnabled field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 15edcc2c-0906-45bc-8fcf-bb38a5b4158a")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox IsCancelRefundQueueEnabled  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IExperienceTest Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/ExperienceTest</para>	
	/// <para>ID: 49e7eaa1-3c70-4c8b-a8eb-28a22d59a986</para>	
	/// </summary>
	[SitecoreType(TemplateId=ExperienceTest.TemplateId )] //, Cachable = true
	public partial interface IExperienceTest : IGlassBase 
	{		/// <summary>
		/// The ExperienceA field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 1f73b906-35d6-4fc1-848d-601169bc32ce</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(ExperienceTest.ExperienceAFieldName)]
		string ExperienceA  {get; set;} 
			/// <summary>
		/// The ExperienceB field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: be11765a-4b33-4301-903f-c4e3d728cf5e</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(ExperienceTest.ExperienceBFieldName)]
		string ExperienceB  {get; set;} 
			/// <summary>
		/// The IsEnabled field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 7a1e8948-5d85-4b53-ae36-54ac530eed9c</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(ExperienceTest.IsEnabledFieldName)]
		bool IsEnabled  {get; set;} 
			/// <summary>
		/// The TestId field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: f57cb232-2d68-4b44-a45f-72ff761a9201</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(ExperienceTest.TestIdFieldName)]
		string TestId  {get; set;} 
			/// <summary>
		/// The TrafficWeights field.
		/// <para></para>
		/// <para>Field Type: Name Value List</para>		
		/// <para>Field ID: 3b4e1feb-736d-42a2-a4ca-d59fc7364409</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(ExperienceTest.TrafficWeightsFieldName)]
		System.Collections.Specialized.NameValueCollection TrafficWeights  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class ExperienceTest
	{
		public const string TemplateId = "49e7eaa1-3c70-4c8b-a8eb-28a22d59a986";
		public const string TemplateName = "ExperienceTest";
				public const string ExperienceAFieldId = "1f73b906-35d6-4fc1-848d-601169bc32ce";
		public const string ExperienceAFieldName = "ExperienceA";			
				public const string ExperienceBFieldId = "be11765a-4b33-4301-903f-c4e3d728cf5e";
		public const string ExperienceBFieldName = "ExperienceB";			
				public const string IsEnabledFieldId = "7a1e8948-5d85-4b53-ae36-54ac530eed9c";
		public const string IsEnabledFieldName = "IsEnabled";			
				public const string TestIdFieldId = "f57cb232-2d68-4b44-a45f-72ff761a9201";
		public const string TestIdFieldName = "TestId";			
				public const string TrafficWeightsFieldId = "3b4e1feb-736d-42a2-a4ca-d59fc7364409";
		public const string TrafficWeightsFieldName = "TrafficWeights";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IExperienceTest interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ExperienceTest template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/ExperienceTest")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 49e7eaa1-3c70-4c8b-a8eb-28a22d59a986")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class ExperienceTest_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public ExperienceTest_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IExperienceTest interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ExperienceA field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 1f73b906-35d6-4fc1-848d-601169bc32ce")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ExperienceA  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ExperienceB field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: be11765a-4b33-4301-903f-c4e3d728cf5e")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ExperienceB  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The IsEnabled field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 7a1e8948-5d85-4b53-ae36-54ac530eed9c")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox IsEnabled  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The TestId field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: f57cb232-2d68-4b44-a45f-72ff761a9201")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText TestId  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The TrafficWeights field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Name Value List")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 3b4e1feb-736d-42a2-a4ca-d59fc7364409")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreNameValueList TrafficWeights  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IPreloadedResource Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/PreloadedResource</para>	
	/// <para>ID: 581a389f-4fe4-4dab-82c5-7c77923f6765</para>	
	/// </summary>
	[SitecoreType(TemplateId=PreloadedResource.TemplateId )] //, Cachable = true
	public partial interface IPreloadedResource : IGlassBase 
	{		/// <summary>
		/// The ResourceName field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 088e33f7-acb4-493e-a174-a94aafed1cb1</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(PreloadedResource.ResourceNameFieldName)]
		string ResourceName  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class PreloadedResource
	{
		public const string TemplateId = "581a389f-4fe4-4dab-82c5-7c77923f6765";
		public const string TemplateName = "PreloadedResource";
				public const string ResourceNameFieldId = "088e33f7-acb4-493e-a174-a94aafed1cb1";
		public const string ResourceNameFieldName = "ResourceName";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IPreloadedResource interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The PreloadedResource template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/PreloadedResource")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 581a389f-4fe4-4dab-82c5-7c77923f6765")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class PreloadedResource_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public PreloadedResource_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IPreloadedResource interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ResourceName field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 088e33f7-acb4-493e-a174-a94aafed1cb1")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ResourceName  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IUserCountrySourceType Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/UserCountrySourceType</para>	
	/// <para>ID: a03c83ac-2cbb-4a64-9a1b-a7a45e7f2f02</para>	
	/// </summary>
	[SitecoreType(TemplateId=UserCountrySourceType.TemplateId )] //, Cachable = true
	public partial interface IUserCountrySourceType : IGlassBase 
	{		/// <summary>
		/// The SourceId field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: a063606b-dc0a-474f-99f6-491d6922e58d</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(UserCountrySourceType.SourceIdFieldName)]
		string SourceId  {get; set;} 
			/// <summary>
		/// The SourceName field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: cf5b9724-fece-4526-aaea-be7de9a4253c</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(UserCountrySourceType.SourceNameFieldName)]
		string SourceName  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class UserCountrySourceType
	{
		public const string TemplateId = "a03c83ac-2cbb-4a64-9a1b-a7a45e7f2f02";
		public const string TemplateName = "UserCountrySourceType";
				public const string SourceIdFieldId = "a063606b-dc0a-474f-99f6-491d6922e58d";
		public const string SourceIdFieldName = "SourceId";			
				public const string SourceNameFieldId = "cf5b9724-fece-4526-aaea-be7de9a4253c";
		public const string SourceNameFieldName = "SourceName";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IUserCountrySourceType interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The UserCountrySourceType template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/UserCountrySourceType")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: a03c83ac-2cbb-4a64-9a1b-a7a45e7f2f02")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class UserCountrySourceType_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public UserCountrySourceType_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IUserCountrySourceType interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The SourceId field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: a063606b-dc0a-474f-99f6-491d6922e58d")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText SourceId  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The SourceName field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: cf5b9724-fece-4526-aaea-be7de9a4253c")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText SourceName  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IHttpCallRetrySettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/HttpCallRetrySettings</para>	
	/// <para>ID: ab6ac9bb-cf8a-4f86-bad7-9e8b053ebc02</para>	
	/// </summary>
	[SitecoreType(TemplateId=HttpCallRetrySettings.TemplateId )] //, Cachable = true
	public partial interface IHttpCallRetrySettings : IGlassBase 
	{		/// <summary>
		/// The ApiName field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 973b9aa2-16f4-489d-8a6f-e2aaf187a2d3</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCallRetrySettings.ApiNameFieldName)]
		string ApiName  {get; set;} 
			/// <summary>
		/// The DelayBetweenRetries field.
		/// <para>in milliseconds</para>
		/// <para>Field Type: Number</para>		
		/// <para>Field ID: fa17aa0e-1593-4951-99a1-bf72e633a1d8</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCallRetrySettings.DelayBetweenRetriesFieldName)]
		float DelayBetweenRetries  {get; set;} 
			/// <summary>
		/// The HttpRetryStatusCodes field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 216d3872-c666-4632-a7a5-7754ef8a4f09</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCallRetrySettings.HttpRetryStatusCodesFieldName)]
		string HttpRetryStatusCodes  {get; set;} 
			/// <summary>
		/// The MaxRetryCount field.
		/// <para></para>
		/// <para>Field Type: Number</para>		
		/// <para>Field ID: 0d4dee5b-968a-40fb-a481-89f80ffce273</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCallRetrySettings.MaxRetryCountFieldName)]
		float MaxRetryCount  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class HttpCallRetrySettings
	{
		public const string TemplateId = "ab6ac9bb-cf8a-4f86-bad7-9e8b053ebc02";
		public const string TemplateName = "HttpCallRetrySettings";
				public const string ApiNameFieldId = "973b9aa2-16f4-489d-8a6f-e2aaf187a2d3";
		public const string ApiNameFieldName = "ApiName";			
				public const string DelayBetweenRetriesFieldId = "fa17aa0e-1593-4951-99a1-bf72e633a1d8";
		public const string DelayBetweenRetriesFieldName = "DelayBetweenRetries";			
				public const string HttpRetryStatusCodesFieldId = "216d3872-c666-4632-a7a5-7754ef8a4f09";
		public const string HttpRetryStatusCodesFieldName = "HttpRetryStatusCodes";			
				public const string MaxRetryCountFieldId = "0d4dee5b-968a-40fb-a481-89f80ffce273";
		public const string MaxRetryCountFieldName = "MaxRetryCount";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IHttpCallRetrySettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The HttpCallRetrySettings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/HttpCallRetrySettings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: ab6ac9bb-cf8a-4f86-bad7-9e8b053ebc02")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class HttpCallRetrySettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public HttpCallRetrySettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IHttpCallRetrySettings interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ApiName field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 973b9aa2-16f4-489d-8a6f-e2aaf187a2d3")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ApiName  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The DelayBetweenRetries field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: in milliseconds")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Number")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: fa17aa0e-1593-4951-99a1-bf72e633a1d8")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreNumber DelayBetweenRetries  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The HttpRetryStatusCodes field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 216d3872-c666-4632-a7a5-7754ef8a4f09")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText HttpRetryStatusCodes  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The MaxRetryCount field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Number")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 0d4dee5b-968a-40fb-a481-89f80ffce273")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreNumber MaxRetryCount  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IPreloadResourceSettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/PreloadResourceSettings</para>	
	/// <para>ID: b48d69b5-6868-4713-bb0d-db71a21593af</para>	
	/// </summary>
	[SitecoreType(TemplateId=PreloadResourceSettings.TemplateId )] //, Cachable = true
	public partial interface IPreloadResourceSettings : IGlassBase 
	{		/// <summary>
		/// The PreloadedResources field.
		/// <para></para>
		/// <para>Field Type: Multilist</para>		
		/// <para>Field ID: 56494b80-6654-44d5-952c-22a35989a086</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(PreloadResourceSettings.PreloadedResourcesFieldName)]
		IEnumerable<Guid> PreloadedResources  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class PreloadResourceSettings
	{
		public const string TemplateId = "b48d69b5-6868-4713-bb0d-db71a21593af";
		public const string TemplateName = "PreloadResourceSettings";
				public const string PreloadedResourcesFieldId = "56494b80-6654-44d5-952c-22a35989a086";
		public const string PreloadedResourcesFieldName = "PreloadedResources";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IPreloadResourceSettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The PreloadResourceSettings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/PreloadResourceSettings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: b48d69b5-6868-4713-bb0d-db71a21593af")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class PreloadResourceSettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public PreloadResourceSettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IPreloadResourceSettings interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The PreloadedResources field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Multilist")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 56494b80-6654-44d5-952c-22a35989a086")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreMultilist PreloadedResources  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IGlobalFrameworkSettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/Global Framework Settings</para>	
	/// <para>ID: bd8abf16-0a38-4a13-91d3-cd9848fadb92</para>	
	/// </summary>
	[SitecoreType(TemplateId=GlobalFrameworkSettings.TemplateId )] //, Cachable = true
	public partial interface IGlobalFrameworkSettings : IGlassBase 
	{	}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class GlobalFrameworkSettings
	{
		public const string TemplateId = "bd8abf16-0a38-4a13-91d3-cd9848fadb92";
		public const string TemplateName = "Global Framework Settings";
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IGlobalFrameworkSettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Global Framework Settings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/Global Framework Settings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: bd8abf16-0a38-4a13-91d3-cd9848fadb92")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class GlobalFrameworkSettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public GlobalFrameworkSettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IGlobalFrameworkSettings interface instead");
		}
			}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IHttpCommunicationDebugging Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/Http Communication Debugging</para>	
	/// <para>ID: c057f0a6-25bf-49c3-b99c-ebf0da1cb22a</para>	
	/// </summary>
	[SitecoreType(TemplateId=HttpCommunicationDebugging.TemplateId )] //, Cachable = true
	public partial interface IHttpCommunicationDebugging : IGlassBase 
	{		/// <summary>
		/// The Enable Debugging field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: e00a190f-0fb5-4989-b058-d9a44274f1be</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCommunicationDebugging.EnableDebuggingFieldName)]
		bool EnableDebugging  {get; set;} 
			/// <summary>
		/// The Enable Request Failing field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: eeb37841-b2d4-4e54-aea6-69040d396bc3</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCommunicationDebugging.EnableRequestFailingFieldName)]
		bool EnableRequestFailing  {get; set;} 
			/// <summary>
		/// The Request Failing Cookie Prefix field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: cd778d86-be1d-47f0-9594-29d084950740</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCommunicationDebugging.RequestFailingCookiePrefixFieldName)]
		string RequestFailingCookiePrefix  {get; set;} 
			/// <summary>
		/// The Enable Response Substitution field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 78c637ca-69b8-4b82-bcaf-fbe64ad5cea6</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCommunicationDebugging.EnableResponseSubstitutionFieldName)]
		bool EnableResponseSubstitution  {get; set;} 
			/// <summary>
		/// The Response Substitution Cookie Prefix field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 66786a71-0360-4d6f-95ef-fc9ecac59d9e</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(HttpCommunicationDebugging.ResponseSubstitutionCookiePrefixFieldName)]
		string ResponseSubstitutionCookiePrefix  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class HttpCommunicationDebugging
	{
		public const string TemplateId = "c057f0a6-25bf-49c3-b99c-ebf0da1cb22a";
		public const string TemplateName = "Http Communication Debugging";
				public const string EnableDebuggingFieldId = "e00a190f-0fb5-4989-b058-d9a44274f1be";
		public const string EnableDebuggingFieldName = "Enable Debugging";			
				public const string EnableRequestFailingFieldId = "eeb37841-b2d4-4e54-aea6-69040d396bc3";
		public const string EnableRequestFailingFieldName = "Enable Request Failing";			
				public const string RequestFailingCookiePrefixFieldId = "cd778d86-be1d-47f0-9594-29d084950740";
		public const string RequestFailingCookiePrefixFieldName = "Request Failing Cookie Prefix";			
				public const string EnableResponseSubstitutionFieldId = "78c637ca-69b8-4b82-bcaf-fbe64ad5cea6";
		public const string EnableResponseSubstitutionFieldName = "Enable Response Substitution";			
				public const string ResponseSubstitutionCookiePrefixFieldId = "66786a71-0360-4d6f-95ef-fc9ecac59d9e";
		public const string ResponseSubstitutionCookiePrefixFieldName = "Response Substitution Cookie Prefix";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IHttpCommunicationDebugging interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Http Communication Debugging template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/Http Communication Debugging")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: c057f0a6-25bf-49c3-b99c-ebf0da1cb22a")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class HttpCommunicationDebugging_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public HttpCommunicationDebugging_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IHttpCommunicationDebugging interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Enable Debugging field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: e00a190f-0fb5-4989-b058-d9a44274f1be")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox EnableDebugging  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Enable Request Failing field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: eeb37841-b2d4-4e54-aea6-69040d396bc3")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox EnableRequestFailing  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Request Failing Cookie Prefix field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: cd778d86-be1d-47f0-9594-29d084950740")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText RequestFailingCookiePrefix  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Enable Response Substitution field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 78c637ca-69b8-4b82-bcaf-fbe64ad5cea6")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox EnableResponseSubstitution  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Response Substitution Cookie Prefix field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 66786a71-0360-4d6f-95ef-fc9ecac59d9e")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ResponseSubstitutionCookiePrefix  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IResetCartSessionCacheLoggingFeatureSwitch Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/FeatureSwitch/Reset Cart Session Cache Logging Feature Switch</para>	
	/// <para>ID: c36d50ea-f9a7-4d95-beeb-7bf2ed0748d2</para>	
	/// </summary>
	[SitecoreType(TemplateId=ResetCartSessionCacheLoggingFeatureSwitch.TemplateId )] //, Cachable = true
	public partial interface IResetCartSessionCacheLoggingFeatureSwitch : IGlassBase 
	{		/// <summary>
		/// The IsLoggingEnabled field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: e9c322a1-419e-4ff1-8543-9979f2092988</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(ResetCartSessionCacheLoggingFeatureSwitch.IsLoggingEnabledFieldName)]
		bool IsLoggingEnabled  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class ResetCartSessionCacheLoggingFeatureSwitch
	{
		public const string TemplateId = "c36d50ea-f9a7-4d95-beeb-7bf2ed0748d2";
		public const string TemplateName = "Reset Cart Session Cache Logging Feature Switch";
				public const string IsLoggingEnabledFieldId = "e9c322a1-419e-4ff1-8543-9979f2092988";
		public const string IsLoggingEnabledFieldName = "IsLoggingEnabled";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IResetCartSessionCacheLoggingFeatureSwitch interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Reset Cart Session Cache Logging Feature Switch template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/FeatureSwitch/Reset Cart Session Cache Logging Feature Switch")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: c36d50ea-f9a7-4d95-beeb-7bf2ed0748d2")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class ResetCartSessionCacheLoggingFeatureSwitch_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public ResetCartSessionCacheLoggingFeatureSwitch_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IResetCartSessionCacheLoggingFeatureSwitch interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The IsLoggingEnabled field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: e9c322a1-419e-4ff1-8543-9979f2092988")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox IsLoggingEnabled  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IUserProductStoreSettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/UserProductStoreSettings</para>	
	/// <para>ID: d93c3a43-79fc-41e1-8044-040ffa354413</para>	
	/// </summary>
	[SitecoreType(TemplateId=UserProductStoreSettings.TemplateId )] //, Cachable = true
	public partial interface IUserProductStoreSettings : IGlassBase 
	{		/// <summary>
		/// The UserCountrySource field.
		/// <para></para>
		/// <para>Field Type: Droplink</para>		
		/// <para>Field ID: 83a05418-e5d7-4227-831f-80d0fb32843c</para>
		/// <para>Custom Data: type=Pmi.Spx.Foundation.Framework.TemplateModels.IUserCountrySourceType</para>
		/// </summary>
		[SitecoreField(UserProductStoreSettings.UserCountrySourceFieldName)]
		Pmi.Spx.Foundation.Framework.TemplateModels.IUserCountrySourceType UserCountrySource  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class UserProductStoreSettings
	{
		public const string TemplateId = "d93c3a43-79fc-41e1-8044-040ffa354413";
		public const string TemplateName = "UserProductStoreSettings";
				public const string UserCountrySourceFieldId = "83a05418-e5d7-4227-831f-80d0fb32843c";
		public const string UserCountrySourceFieldName = "UserCountrySource";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IUserProductStoreSettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The UserProductStoreSettings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/UserProductStoreSettings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: d93c3a43-79fc-41e1-8044-040ffa354413")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class UserProductStoreSettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public UserProductStoreSettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IUserProductStoreSettings interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The UserCountrySource field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Droplink")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 83a05418-e5d7-4227-831f-80d0fb32843c")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: type=Pmi.Spx.Foundation.Framework.TemplateModels.IUserCountrySourceType")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreDroplink UserCountrySource  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Foundation.Framework.TemplateModels
{
 	/// <summary>
	/// IUserCountrySourceTypes Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/UserCountrySourceTypes</para>	
	/// <para>ID: e585a758-eef8-4acd-9be7-061e701df769</para>	
	/// </summary>
	[SitecoreType(TemplateId=UserCountrySourceTypes.TemplateId )] //, Cachable = true
	public partial interface IUserCountrySourceTypes : IGlassBase 
	{	}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class UserCountrySourceTypes
	{
		public const string TemplateId = "e585a758-eef8-4acd-9be7-061e701df769";
		public const string TemplateName = "UserCountrySourceTypes";
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IUserCountrySourceTypes interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The UserCountrySourceTypes template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Foundation/Framework/Global Settings/UserCountrySourceTypes")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: e585a758-eef8-4acd-9be7-061e701df769")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class UserCountrySourceTypes_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public UserCountrySourceTypes_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IUserCountrySourceTypes interface instead");
		}
			}
	#endif
}
